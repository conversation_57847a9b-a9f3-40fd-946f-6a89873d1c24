const ApplicationJob = require('./ApplicationJob');
const { QdrantClient } = require('@qdrant/js-client-rest');
const { JobVacancy } = require('../models');
const OnetService = require('../services/OnetService');
const GenerateJobDescService = require('../services/job_vacancy/GenerateJobDescService');
const GoogleAiService = require('../services/external/GoogleAiService');
const config = require('../config/config');

class SetJobDescriptionJob extends ApplicationJob {
  constructor() {
    super();
    this.onetService = new OnetService();
    this.googleAiService = new GoogleAiService();
    this.qdrantClient = new QdrantClient({
      url: config.qdrantUrl,
      apiKey: config.qdrantApiKey,
    });
    this.generateJobDescService = new GenerateJobDescService({
      onetService: this.onetService,
      googleAiService: this.googleAiService,
      qdrantClient: this.qdrantClient,
    });
  }

  /**
   * Process the job description generation
   * @param {Object} data - Job data containing vacancy_id
   * @returns {Promise<Object>} Job result
   */
  async perform(data) {
    const { vacancy_id } = data;

    if (!vacancy_id) {
      throw new Error('vacancy_id is required for SetJobDescriptionJob');
    }

    // Fetch the vacancy with its related data
    const vacancy = await JobVacancy.findByPk(vacancy_id, {
      include: ['jobLevel'],
    });

    if (!vacancy) {
      throw new Error(`Job vacancy with id ${vacancy_id} not found`);
    }

    try {
      const jobTitleName = vacancy.name;
      const relatedUserIds = vacancy.related_user_ids;
      const jobLevelName = vacancy.jobLevel ? vacancy.jobLevel.name : '';
      const roleSummary = vacancy.role_summary;

      console.log(`Generating job description for vacancy ${vacancy_id}: ${jobTitleName}`);

      const generatedJobDesc = await this.generateJobDescService.generateJobDesc(
        jobTitleName,
        relatedUserIds,
        jobLevelName,
        roleSummary,
      );

      await vacancy.update({
        job_desc: generatedJobDesc.jobDescription.key_responsibilities,
        related_onetsoc_codes: generatedJobDesc.onetsocCodes,
        status: 'draft',
        detailed_descriptions: generatedJobDesc.jobDescription,
      });

      console.log(`Successfully generated job description for vacancy ${vacancy_id}`);

      return {
        vacancy_id,
        status: 'completed',
        job_description: generatedJobDesc.jobDescription,
        onetsoc_codes: generatedJobDesc.onetsocCodes,
      };
    } catch (error) {
      console.error(`Error generating job description for vacancy ${vacancy_id}:`, error);

      // Update vacancy status to draft even on error
      await vacancy.update({ status: 'draft' });

      throw error;
    }
  }

  /**
   * Hook called before job processing
   * @param {Object} data - Job data
   */
  async before_perform(data) {
    console.log(`Starting SetJobDescriptionJob for vacancy ${data.vacancy_id}`);
  }

  /**
   * Hook called after successful job processing
   * @param {Object} data - Job data
   * @param {any} result - Job result
   */
  async after_perform(data, _result) {
    console.log(`Completed SetJobDescriptionJob for vacancy ${data.vacancy_id}`);
  }

  /**
   * Hook called when job fails
   * @param {Object} data - Job data
   * @param {Error} error - The error that occurred
   */
  async on_failure(data, error) {
    console.error(`SetJobDescriptionJob failed for vacancy ${data.vacancy_id}:`, error);

    // Additional error handling could be added here, such as:
    // - Sending notifications
    // - Updating external systems
    // - Logging to error tracking services
  }
}

module.exports = SetJobDescriptionJob;
