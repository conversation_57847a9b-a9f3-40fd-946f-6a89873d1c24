// Mock Redis and BullMQ before importing ApplicationJob
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    quit: jest.fn().mockResolvedValue(undefined),
  }));
});

jest.mock('bullmq', () => ({
  Queue: jest.fn().mockImplementation(() => ({
    add: jest.fn().mockResolvedValue({ id: 'job-123' }),
    close: jest.fn().mockResolvedValue(undefined),
  })),
  Worker: jest.fn().mockImplementation(() => ({
    on: jest.fn(),
  })),
}));

const ApplicationJob = require('../../src/jobs/ApplicationJob');
const { Queue } = require('bullmq');
const Redis = require('ioredis');

describe('ApplicationJob', () => {
  let mockRedis;
  let mockQueue;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Create mock instances
    mockRedis = {
      quit: jest.fn().mockResolvedValue(undefined),
    };

    mockQueue = {
      add: jest.fn().mockResolvedValue({ id: 'job-123' }),
      close: jest.fn().mockResolvedValue(undefined),
    };

    // Reset mock implementations
    Redis.mockImplementation(() => mockRedis);
    Queue.mockImplementation(() => mockQueue);
  });

  describe('constructor', () => {
    it('should initialize with default queue name and Redis connection', () => {
      const job = new ApplicationJob();

      expect(job.queueName).toBe('default');
      expect(Redis).toHaveBeenCalledWith(
        expect.objectContaining({
          host: 'localhost',
          port: 6379,
        }),
      );
      expect(Queue).toHaveBeenCalledWith(
        'default',
        expect.objectContaining({
          connection: mockRedis,
        }),
      );
    });
  });

  describe('perform_async', () => {
    it('should enqueue a job with default options', async () => {
      const jobData = { test: 'data' };

      const result = await ApplicationJob.perform_async(jobData);

      expect(mockQueue.add).toHaveBeenCalledWith(
        'ApplicationJob',
        jobData,
        expect.objectContaining({
          priority: 0,
          delay: 0,
          attempts: 3,
        }),
      );
      expect(result).toEqual({ id: 'job-123' });
    });

    it('should enqueue a job with custom options', async () => {
      const jobData = { test: 'data' };
      const options = {
        priority: 1,
        delay: 5000,
        attempts: 5,
      };

      await ApplicationJob.perform_async(jobData, options);

      expect(mockQueue.add).toHaveBeenCalledWith(
        'ApplicationJob',
        jobData,
        expect.objectContaining({
          priority: 1,
          delay: 5000,
          attempts: 5,
        }),
      );
    });
  });

  describe('perform', () => {
    it('should throw error when not implemented', async () => {
      const job = new ApplicationJob();

      await expect(job.perform({})).rejects.toThrow(
        'ApplicationJob must implement the perform method',
      );
    });
  });

  describe('hooks', () => {
    let job;

    beforeEach(() => {
      job = new ApplicationJob();
    });

    it('should have default before_perform hook', async () => {
      await expect(job.before_perform({})).resolves.toBeUndefined();
    });

    it('should have default after_perform hook', async () => {
      await expect(job.after_perform({}, 'result')).resolves.toBeUndefined();
    });

    it('should have default on_failure hook that logs error', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const error = new Error('Test error');

      await job.on_failure({}, error);

      expect(consoleSpy).toHaveBeenCalledWith('Job ApplicationJob failed:', error);

      consoleSpy.mockRestore();
    });
  });

  describe('getQueue', () => {
    it('should return the queue instance', () => {
      const job = new ApplicationJob();
      expect(job.getQueue()).toBe(mockQueue);
    });
  });

  describe('getRedis', () => {
    it('should return the Redis instance', () => {
      const job = new ApplicationJob();
      expect(job.getRedis()).toBe(mockRedis);
    });
  });

  describe('close', () => {
    it('should close queue and Redis connections', async () => {
      const job = new ApplicationJob();

      await job.close();

      expect(mockQueue.close).toHaveBeenCalled();
      expect(mockRedis.quit).toHaveBeenCalled();
    });
  });
});

// Test custom job class
class TestJob extends ApplicationJob {
  async perform(data) {
    return { processed: data };
  }

  async before_perform(_data) {
    this.beforeCalled = true;
  }

  async after_perform(_data, _result) {
    this.afterCalled = true;
  }

  async on_failure(_data, _error) {
    this.failureCalled = true;
  }
}

describe('Custom Job Implementation', () => {
  let mockRedis;
  let mockQueue;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock instances
    mockRedis = {
      quit: jest.fn().mockResolvedValue(undefined),
    };

    mockQueue = {
      add: jest.fn().mockResolvedValue({ id: 'job-123' }),
      close: jest.fn().mockResolvedValue(undefined),
    };

    // Reset mock implementations
    Redis.mockImplementation(() => mockRedis);
    Queue.mockImplementation(() => mockQueue);
  });

  it('should implement perform method', async () => {
    const job = new TestJob();
    const data = { test: 'data' };

    const result = await job.perform(data);

    expect(result).toEqual({ processed: data });
  });

  it('should call hooks in correct order', async () => {
    const job = new TestJob();
    const data = { test: 'data' };

    await job.before_perform(data);
    const result = await job.perform(data);
    await job.after_perform(data, result);

    expect(job.beforeCalled).toBe(true);
    expect(job.afterCalled).toBe(true);
  });

  it('should call on_failure hook on error', async () => {
    const job = new TestJob();
    const data = { test: 'data' };
    const error = new Error('Test error');

    await job.on_failure(data, error);

    expect(job.failureCalled).toBe(true);
  });
});
