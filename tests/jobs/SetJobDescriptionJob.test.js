const SetJobDescriptionJob = require('../../src/jobs/SetJobDescriptionJob');
const { JobVacancy } = require('../../src/models');
const GenerateJobDescService = require('../../src/services/job_vacancy/GenerateJobDescService');

// Mock dependencies
jest.mock('../../src/models');
jest.mock('../../src/services/OnetService');
jest.mock('../../src/services/job_vacancy/GenerateJobDescService');
jest.mock('../../src/services/external/GoogleAiService');
jest.mock('@qdrant/js-client-rest');
jest.mock('ioredis');
jest.mock('bullmq');

describe('SetJobDescriptionJob', () => {
  let job;
  let mockVacancy;
  let mockGenerateJobDescService;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock vacancy
    mockVacancy = {
      id: 1,
      name: 'Software Engineer',
      related_user_ids: [1, 2],
      role_summary: 'Test role summary',
      jobLevel: { name: 'Senior' },
      update: jest.fn().mockResolvedValue(true),
    };

    // Mock JobVacancy.findByPk
    JobVacancy.findByPk = jest.fn().mockResolvedValue(mockVacancy);

    // Mock GenerateJobDescService
    mockGenerateJobDescService = {
      generateJobDesc: jest.fn().mockResolvedValue({
        jobDescription: {
          key_responsibilities: ['Responsibility 1', 'Responsibility 2'],
          qualifications: ['Qualification 1'],
          competencies: ['Competency 1'],
          success_metrics: ['Metric 1'],
        },
        onetsocCodes: ['15-1132.00', '15-1133.00'],
      }),
    };

    GenerateJobDescService.mockImplementation(() => mockGenerateJobDescService);

    job = new SetJobDescriptionJob();
  });

  describe('perform', () => {
    it('should successfully generate job description', async () => {
      const data = { vacancy_id: 1 };

      const result = await job.perform(data);

      expect(JobVacancy.findByPk).toHaveBeenCalledWith(1, {
        include: ['jobLevel'],
      });

      expect(mockGenerateJobDescService.generateJobDesc).toHaveBeenCalledWith(
        'Software Engineer',
        [1, 2],
        'Senior',
        'Test role summary',
      );

      expect(mockVacancy.update).toHaveBeenCalledWith({
        job_desc: ['Responsibility 1', 'Responsibility 2'],
        related_onetsoc_codes: ['15-1132.00', '15-1133.00'],
        status: 'draft',
        detailed_descriptions: {
          key_responsibilities: ['Responsibility 1', 'Responsibility 2'],
          qualifications: ['Qualification 1'],
          competencies: ['Competency 1'],
          success_metrics: ['Metric 1'],
        },
      });

      expect(result).toEqual({
        vacancy_id: 1,
        status: 'completed',
        job_description: {
          key_responsibilities: ['Responsibility 1', 'Responsibility 2'],
          qualifications: ['Qualification 1'],
          competencies: ['Competency 1'],
          success_metrics: ['Metric 1'],
        },
        onetsoc_codes: ['15-1132.00', '15-1133.00'],
      });
    });

    it('should handle vacancy without job level', async () => {
      mockVacancy.jobLevel = null;
      const data = { vacancy_id: 1 };

      await job.perform(data);

      expect(mockGenerateJobDescService.generateJobDesc).toHaveBeenCalledWith(
        'Software Engineer',
        [1, 2],
        '',
        'Test role summary',
      );
    });

    it('should throw error when vacancy_id is missing', async () => {
      const data = {};

      await expect(job.perform(data)).rejects.toThrow(
        'vacancy_id is required for SetJobDescriptionJob',
      );
    });

    it('should throw error when vacancy is not found', async () => {
      JobVacancy.findByPk.mockResolvedValue(null);
      const data = { vacancy_id: 999 };

      await expect(job.perform(data)).rejects.toThrow('Job vacancy with id 999 not found');
    });

    it('should handle errors and update vacancy status to draft', async () => {
      const error = new Error('Generation failed');
      mockGenerateJobDescService.generateJobDesc.mockRejectedValue(error);
      const data = { vacancy_id: 1 };

      await expect(job.perform(data)).rejects.toThrow('Generation failed');

      expect(mockVacancy.update).toHaveBeenCalledWith({ status: 'draft' });
    });
  });

  describe('hooks', () => {
    it('should log start message in before_perform', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const data = { vacancy_id: 1 };

      await job.before_perform(data);

      expect(consoleSpy).toHaveBeenCalledWith('Starting SetJobDescriptionJob for vacancy 1');

      consoleSpy.mockRestore();
    });

    it('should log completion message in after_perform', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const data = { vacancy_id: 1 };
      const result = { status: 'completed' };

      await job.after_perform(data, result);

      expect(consoleSpy).toHaveBeenCalledWith('Completed SetJobDescriptionJob for vacancy 1');

      consoleSpy.mockRestore();
    });

    it('should log error message in on_failure', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const data = { vacancy_id: 1 };
      const error = new Error('Test error');

      await job.on_failure(data, error);

      expect(consoleSpy).toHaveBeenCalledWith('SetJobDescriptionJob failed for vacancy 1:', error);

      consoleSpy.mockRestore();
    });
  });

  describe('integration with ApplicationJob', () => {
    it('should be able to enqueue jobs', async () => {
      const mockQueue = {
        add: jest.fn().mockResolvedValue({ id: 'job-123' }),
      };

      // Mock the queue creation
      job.queue = mockQueue;

      const result = await SetJobDescriptionJob.perform_async({ vacancy_id: 1 });

      expect(result).toBeDefined();
    });
  });
});
