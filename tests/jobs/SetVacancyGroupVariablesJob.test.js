const SetVacancyGroupVariablesJob = require('../../src/jobs/SetVacancyGroupVariablesJob');
const { JobVacancy, VacancyGroupVariable, Bone, sequelize } = require('../../src/models');
const OnetService = require('../../src/services/OnetService');
const GoogleAiService = require('../../src/services/external/GoogleAiService');

// Mock dependencies
jest.mock('../../src/models');
jest.mock('../../src/services/OnetService');
jest.mock('../../src/services/external/GoogleAiService');
jest.mock('@qdrant/js-client-rest');
jest.mock('ioredis');
jest.mock('bullmq');

describe('SetVacancyGroupVariablesJob', () => {
  let job;
  let mockVacancy;
  let mockOnetService;
  let mockGoogleAiService;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock vacancy
    mockVacancy = {
      id: 1,
      name: 'Software Engineer',
      bone_id: 1,
      detailed_descriptions: {
        key_responsibilities: ['Develop software', 'Test applications'],
      },
      job_desc: ['Develop software'],
      related_onetsoc_codes: ['15-1132.00'],
      update: jest.fn().mockResolvedValue(true),
    };

    // Mock JobVacancy.findByPk
    JobVacancy.findByPk = jest.fn().mockResolvedValue(mockVacancy);

    // Mock Bone.findByPk
    Bone.findByPk = jest.fn().mockResolvedValue({ name: 'Technical' });

    // Mock VacancyGroupVariable.bulkCreate
    VacancyGroupVariable.bulkCreate = jest.fn().mockResolvedValue([]);

    // Mock sequelize.query
    sequelize.query = jest.fn().mockResolvedValue([
      { id: 1, keywords: ['javascript', 'programming'] },
      { id: 2, keywords: ['testing', 'quality'] },
    ]);

    // Mock OnetService
    mockOnetService = {
      getOccupations: jest.fn().mockResolvedValue({
        '15-1132.00': { title: 'Software Developer', description: 'Develops software' },
      }),
      getKnowledges: jest.fn().mockResolvedValue([]),
      getSkills: jest.fn().mockResolvedValue([]),
      getAbilities: jest.fn().mockResolvedValue([]),
      getInterests: jest.fn().mockResolvedValue([]),
      getWorkValues: jest.fn().mockResolvedValue([]),
      getWorkStyles: jest.fn().mockResolvedValue([]),
    };
    OnetService.mockImplementation(() => mockOnetService);

    // Mock GoogleAiService
    mockGoogleAiService = {
      generateContent: jest.fn().mockResolvedValue({
        candidates: [
          {
            content: {
              parts: [
                {
                  text: JSON.stringify({
                    knowledge: ['Programming', 'Software Development'],
                    skills: ['JavaScript', 'Testing'],
                    abilities: ['Problem Solving', 'Analytical Thinking'],
                    other_characteristics: ['Detail Oriented', 'Team Player'],
                  }),
                },
              ],
            },
          },
        ],
      }),
    };
    GoogleAiService.mockImplementation(() => mockGoogleAiService);

    job = new SetVacancyGroupVariablesJob();
  });

  describe('perform', () => {
    it('should successfully generate vacancy group variables', async () => {
      const data = { vacancy_id: 1 };

      const result = await job.perform(data);

      expect(JobVacancy.findByPk).toHaveBeenCalledWith(1);
      expect(mockGoogleAiService.generateContent).toHaveBeenCalled();
      expect(VacancyGroupVariable.bulkCreate).toHaveBeenCalled();
      expect(mockVacancy.update).toHaveBeenCalledWith({
        ksao: expect.any(Object),
        status: 'draft',
      });

      expect(result).toEqual({
        vacancy_id: 1,
        status: 'completed',
        ksao: expect.any(Object),
        vgv_records_count: expect.any(Number),
      });
    });

    it('should throw error when vacancy_id is missing', async () => {
      const data = {};

      await expect(job.perform(data)).rejects.toThrow(
        'vacancy_id is required for SetVacancyGroupVariablesJob',
      );
    });

    it('should throw error when vacancy is not found', async () => {
      JobVacancy.findByPk.mockResolvedValue(null);
      const data = { vacancy_id: 999 };

      await expect(job.perform(data)).rejects.toThrow('Job vacancy with id 999 not found');
    });

    it('should handle errors and update vacancy status to draft', async () => {
      const error = new Error('Generation failed');
      mockGoogleAiService.generateContent.mockRejectedValue(error);
      const data = { vacancy_id: 1 };

      await expect(job.perform(data)).rejects.toThrow('Generation failed');

      expect(mockVacancy.update).toHaveBeenCalledWith({ status: 'draft' });
    });
  });

  describe('generateKsao', () => {
    it('should generate KSAO from vacancy data', async () => {
      const result = await job.generateKsao(mockVacancy);

      expect(mockGoogleAiService.generateContent).toHaveBeenCalledWith({
        model: 'gemini-2.5-flash',
        contents: [{ role: 'user', parts: [{ text: expect.any(String) }] }],
        config: {
          temperature: 0.2,
          responseMimeType: 'application/json',
          thinkingConfig: { thinkingBudget: -1 },
          systemInstruction: [{ text: expect.any(String) }],
        },
      });

      expect(result).toEqual({
        knowledge: ['Programming', 'Software Development'],
        skills: ['JavaScript', 'Testing'],
        abilities: ['Problem Solving', 'Analytical Thinking'],
        other_characteristics: ['Detail Oriented', 'Team Player'],
      });
    });
  });

  describe('getOccupationsData', () => {
    it('should fetch and merge occupation data', async () => {
      const onetsocCodes = ['15-1132.00'];

      const result = await job.getOccupationsData(onetsocCodes);

      expect(mockOnetService.getOccupations).toHaveBeenCalledWith(onetsocCodes);
      expect(mockOnetService.getKnowledges).toHaveBeenCalledWith(onetsocCodes);
      expect(mockOnetService.getSkills).toHaveBeenCalledWith(onetsocCodes);
      expect(mockOnetService.getAbilities).toHaveBeenCalledWith(onetsocCodes);
      expect(mockOnetService.getInterests).toHaveBeenCalledWith(onetsocCodes);
      expect(mockOnetService.getWorkValues).toHaveBeenCalledWith(onetsocCodes);
      expect(mockOnetService.getWorkStyles).toHaveBeenCalledWith(onetsocCodes);

      expect(result).toEqual({
        '15-1132.00': expect.objectContaining({
          title: 'Software Developer',
          description: 'Develops software',
          knowledges: [],
          skills: [],
          abilities: [],
          interests: [],
          work_values: [],
          work_styles: [],
        }),
      });
    });

    it('should return empty object for empty onetsoc codes', async () => {
      const result = await job.getOccupationsData([]);
      expect(result).toEqual({});
    });

    it('should return empty object for null onetsoc codes', async () => {
      const result = await job.getOccupationsData(null);
      expect(result).toEqual({});
    });
  });

  describe('ksaoMatching', () => {
    it('should match KSAO with job group variables', async () => {
      const ksao = {
        knowledge: ['Programming'],
        skills: ['JavaScript'],
        abilities: ['Problem Solving'],
        other_characteristics: ['Detail Oriented'],
      };

      const result = await job.ksaoMatching({
        ksao,
        jobVacancyId: 1,
        boneId: 1,
      });

      expect(sequelize.query).toHaveBeenCalledWith('SELECT id, keywords FROM job_group_variables', {
        type: sequelize.QueryTypes.SELECT,
      });

      expect(Bone.findByPk).toHaveBeenCalledWith(1);

      expect(result).toEqual([
        expect.objectContaining({
          job_vacancy_id: 1,
          job_group_variable_id: 1,
          keyword_match_count: expect.any(Number),
          keyword_total_count: 2,
          match_type: 'weight',
          weight: 0.5,
          bone_value: expect.any(Number),
        }),
        expect.objectContaining({
          job_vacancy_id: 1,
          job_group_variable_id: 2,
          keyword_match_count: expect.any(Number),
          keyword_total_count: 2,
          match_type: 'weight',
          weight: 0.5,
          bone_value: expect.any(Number),
        }),
      ]);
    });
  });

  describe('flattenKsao', () => {
    it('should flatten KSAO object to lowercase string', async () => {
      const ksao = {
        knowledge: ['Programming', 'Software Development'],
        skills: ['JavaScript', 'Testing'],
      };

      const result = await job.flattenKsao(ksao);

      expect(result).toBe('programming;software development;javascript;testing');
    });
  });

  describe('hooks', () => {
    it('should log start message in before_perform', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const data = { vacancy_id: 1 };

      await job.before_perform(data);

      expect(consoleSpy).toHaveBeenCalledWith('Starting SetVacancyGroupVariablesJob for vacancy 1');

      consoleSpy.mockRestore();
    });

    it('should log completion message in after_perform', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const data = { vacancy_id: 1 };
      const result = { status: 'completed' };

      await job.after_perform(data, result);

      expect(consoleSpy).toHaveBeenCalledWith(
        'Completed SetVacancyGroupVariablesJob for vacancy 1',
      );

      consoleSpy.mockRestore();
    });

    it('should log error message in on_failure', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const data = { vacancy_id: 1 };
      const error = new Error('Test error');

      await job.on_failure(data, error);

      expect(consoleSpy).toHaveBeenCalledWith(
        'SetVacancyGroupVariablesJob failed for vacancy 1:',
        error,
      );

      consoleSpy.mockRestore();
    });
  });
});
